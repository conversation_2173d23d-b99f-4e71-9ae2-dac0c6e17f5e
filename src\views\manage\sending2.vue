<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="推送范围" prop="pushScope">
                <el-select v-model="queryParams.pushScope" placeholder="请选择推送范围" clearable style="width: 160px;">
                    <el-option label="属性" value="attribute" />
                    <el-option label="单位" value="company" />
                </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 160px;">
                    <el-option label="待审核" value="pending" />
                    <el-option label="已审核" value="approved" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
                <el-button icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd"
                    v-hasPermi="['message-created']">新建</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list">
            <el-table-column label="ID" align="center" prop="id" />
            <el-table-column label="推送范围" align="center" prop="pushScope">
                <template #default="scope">
                    <el-tag v-if="scope.row.pushScope === 'attribute'" type="primary">属性</el-tag>
                    <el-tag v-else-if="scope.row.pushScope === 'company'" type="success">单位</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="发送时间" align="center" prop="sendTime" />
            <el-table-column label="状态" align="center" prop="status">
                <template #default="scope">
                    <el-tag v-if="scope.row.status === 'pending'" type="warning">待审核</el-tag>
                    <el-tag v-else-if="scope.row.status === 'approved'" type="success">已审核</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="280" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
                    <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)"
                        v-if="scope.row.status === 'pending'" v-hasPermi="['message-created']">编辑</el-button>
                    <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                        v-if="scope.row.status === 'pending'" v-hasPermi="['message-created']">删除</el-button>
                    <el-button link type="success" icon="Check" @click="handleApprove(scope.row)"
                        v-if="scope.row.status === 'pending'" v-hasPermi="['message-checked']">审核</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改政策发送对话框 -->
        <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body @close="handleClose">
            <el-form ref="dataForm" :model="dataForm" :rules="dynamicRules" label-width="120px">
                <el-form-item label="推送范围" prop="pushScope">
                    <el-radio-group v-model="dataForm.pushScope" @change="handlePushScopeChange" :disabled="isView">
                        <el-radio value="attribute">属性</el-radio>
                        <el-radio value="company">单位</el-radio>
                    </el-radio-group>
                </el-form-item>

                <!-- 属性选择 -->
                <div v-if="dataForm.pushScope === 'attribute'">
                    <el-form-item label="行业" prop="industry">
                        <el-select v-model="dataForm.industry" placeholder="请选择行业" style="width: 100%"
                            :disabled="isView">
                            <el-option v-for="item in industryOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="部门" prop="department">
                        <el-select v-model="dataForm.department" placeholder="请选择部门" style="width: 100%"
                            :disabled="isView">
                            <el-option v-for="item in departmentOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="属地" prop="territory">
                        <el-select v-model="dataForm.territory" placeholder="请选择属地" style="width: 100%"
                            :disabled="isView">
                            <el-option v-for="item in territoryOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </div>

                <!-- 单位选择 -->
                <div v-if="dataForm.pushScope === 'company'">
                    <el-form-item label="单位名称" prop="companyName">
                        <el-select v-model="dataForm.companyName" placeholder="请选择单位名称" style="width: 100%"
                            :disabled="isView">
                            <el-option v-for="item in companyOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </el-form-item>
                </div>

                <el-form-item label="内容" prop="content">
                    <editor v-model="dataForm.content" :min-height="200" :readOnly="isView" />
                </el-form-item>

                <el-form-item label="附件上传" v-if="!isView">
                    <file-upload v-model="dataForm.attachments" :showTip="false" />
                </el-form-item>

                <!-- 查看模式下显示附件 -->
                <el-form-item label="附件" v-if="isView && dataForm.attachments">
                    <file-upload v-model="dataForm.attachments" :disabled="true" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">{{ isView ? '确 定' : '发 送' }}</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 审核对话框 -->
        <el-dialog title="审核政策发送" v-model="approveDialogVisible" width="600px" append-to-body>
            <el-form ref="approveForm" :model="approveForm" :rules="approveRules" label-width="120px">
                <el-form-item label="审核" prop="approveResult">
                    <el-radio-group v-model="approveForm.approveResult">
                        <el-radio value="approved">通过</el-radio>
                        <el-radio value="rejected">拒绝</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注" prop="approveComment">
                    <el-input v-model="approveForm.approveComment" type="textarea" :rows="4" placeholder="请输入审核意见" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitApprove">确 定</el-button>
                    <el-button @click="approveDialogVisible = false">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { getPolicyList, addPolicy, updatePolicy, getPolicy, delPolicy, approvePolicy } from "@/api/manage/policy";
import { getIndustryList } from "@/api/manage/industry";
import { getDepartmentList } from "@/api/manage/department";
import { getTerritoryList } from "@/api/manage/territory";
import { getList as getCompanyList } from "@/api/manage/contacts"; // 假设使用联系人API获取单位列表

export default {
    data() {
        return {
            showSearch: true,
            isChange: false,
            isView: false,
            list: [],
            loading: false,
            queryParams: {
                pushScope: '',
                status: '',
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            dialogVisible: false,
            approveDialogVisible: false,
            title: '',
            editId: null,
            dataForm: {
                pushScope: "",
                industry: "",
                department: "",
                territory: "",
                companyName: "",
                content: "",
                attachments: ""
            },
            approveForm: {
                approveResult: "",
                approveComment: ""
            },
            // 选项数据
            industryOptions: [],
            departmentOptions: [],
            territoryOptions: [],
            companyOptions: [],
            dataRules: {
                pushScope: [
                    { required: true, message: '推送范围不能为空', trigger: 'change' }
                ],
                content: [
                    { required: true, message: '内容不能为空', trigger: 'blur' }
                ]
            },
            approveRules: {
                approveResult: [
                    { required: true, message: '审核结果不能为空', trigger: 'change' }
                ],
                approveComment: [
                    { required: true, message: '审核意见不能为空', trigger: 'blur' }
                ]
            }
        }
    },
    computed: {
        // 动态验证规则
        dynamicRules() {
            const rules = {
                pushScope: [
                    { required: true, message: '推送范围不能为空', trigger: 'change' }
                ],
                content: [
                    { required: true, message: '内容不能为空', trigger: 'blur' }
                ]
            };

            // 根据推送范围添加相应的验证规则
            if (this.dataForm.pushScope === 'attribute') {
                rules.industry = [
                    { required: true, message: '行业不能为空', trigger: 'change' }
                ];
                rules.department = [
                    { required: true, message: '部门不能为空', trigger: 'change' }
                ];
                rules.territory = [
                    { required: true, message: '属地不能为空', trigger: 'change' }
                ];
            } else if (this.dataForm.pushScope === 'company') {
                rules.companyName = [
                    { required: true, message: '单位名称不能为空', trigger: 'change' }
                ];
            }

            return rules;
        }
    },
    mounted() {
        this.getList();
        this.loadOptions();
    },
    methods: {
        // 加载选项数据
        async loadOptions() {
            try {
                // 加载行业选项
                const industryRes = await getIndustryList({ pageNum: 1, pageSize: 1000 });
                if (industryRes.code === 200) {
                    this.industryOptions = industryRes.rows.map(item => ({
                        label: item.name,
                        value: item.id
                    }));
                }

                // 加载部门选项
                const departmentRes = await getDepartmentList({ pageNum: 1, pageSize: 1000 });
                if (departmentRes.code === 200) {
                    this.departmentOptions = departmentRes.rows.map(item => ({
                        label: item.name,
                        value: item.id
                    }));
                }

                // 加载属地选项
                const territoryRes = await getTerritoryList({ pageNum: 1, pageSize: 1000 });
                if (territoryRes.code === 200) {
                    this.territoryOptions = territoryRes.rows.map(item => ({
                        label: item.name,
                        value: item.id
                    }));
                }

                // 加载单位选项
                const companyRes = await getCompanyList({ pageNum: 1, pageSize: 1000 });
                if (companyRes.code === 200) {
                    this.companyOptions = companyRes.rows.map(item => ({
                        label: item.unitName,
                        value: item.id
                    }));
                }
            } catch (error) {
                console.error('加载选项数据失败:', error);
            }
        },

        // 推送范围变化处理
        handlePushScopeChange() {
            // 清空相关字段
            this.dataForm.industry = "";
            this.dataForm.department = "";
            this.dataForm.territory = "";
            this.dataForm.companyName = "";
        },

        submitForm() {
            if (this.isView) {
                this.dialogVisible = false;
                return;
            }

            this.$refs.dataForm.validate(async (valid) => {
                if (valid) {
                    try {
                        if (this.isChange) {
                            let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                            dataForm.id = this.editId;
                            let res = await updatePolicy(dataForm);
                            if (res.code === 200) {
                                this.$message({
                                    message: '修改成功',
                                    type: 'success'
                                });
                                this.getList();
                                this.dialogVisible = false;
                            }
                        } else {
                            let dataForm = JSON.parse(JSON.stringify(this.dataForm));
                            let res = await addPolicy(dataForm);
                            if (res.code === 200) {
                                this.$message({
                                    message: '发送成功，等待审核',
                                    type: 'success'
                                });
                                this.getList();
                                this.dialogVisible = false;
                            }
                        }
                    } catch (error) {
                        console.error('提交失败:', error);
                        this.$message({
                            message: '操作失败',
                            type: 'error'
                        });
                    }
                } else {
                    return false;
                }
            });
        },
        handleAdd() {
            this.isChange = false;
            this.isView = false;
            this.title = "新建政策发送";
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields();
                this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm));
            });
        },

        handleClose() {
            this.$refs['dataForm'].resetFields();
            this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm));
            this.dialogVisible = false;
            this.isView = false;
        },

        // 查看
        async handleView(val) {
            this.title = "查看政策发送";
            this.isView = true;
            try {
                let res = await getPolicy(val.id);
                if (res.code === 200) {
                    this.dataForm = res.data;
                    this.editId = val.id;
                }
                this.dialogVisible = true;
                this.isChange = false;
            } catch (error) {
                console.error('获取详情失败:', error);
            }
        },

        // 编辑
        async handleEdit(val) {
            this.title = "修改政策发送";
            this.isView = false;
            try {
                let res = await getPolicy(val.id);
                if (res.code === 200) {
                    this.dataForm = res.data;
                    this.editId = val.id;
                }
                this.dialogVisible = true;
                this.isChange = true;
            } catch (error) {
                console.error('获取详情失败:', error);
            }
        },

        // 删除
        handleDelete(val) {
            this.$confirm('确认删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                try {
                    let res = await delPolicy(val.id);
                    if (res.code === 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getList();
                    }
                } catch (error) {
                    console.error('删除失败:', error);
                    this.$message({
                        message: '删除失败',
                        type: 'error'
                    });
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
        },

        // 审核
        handleApprove(val) {
            this.editId = val.id;
            this.approveForm = {
                approveResult: "",
                approveComment: ""
            };
            this.approveDialogVisible = true;
        },

        // 提交审核
        submitApprove() {
            this.$refs.approveForm.validate(async (valid) => {
                if (valid) {
                    try {
                        let approveData = {
                            id: this.editId,
                            ...this.approveForm
                        };
                        let res = await approvePolicy(approveData);
                        if (res.code === 200) {
                            this.$message({
                                message: '审核成功',
                                type: 'success'
                            });
                            this.getList();
                            this.approveDialogVisible = false;
                        }
                    } catch (error) {
                        console.error('审核失败:', error);
                        this.$message({
                            message: '审核失败',
                            type: 'error'
                        });
                    }
                }
            });
        },
        // 重置搜索
        reset() {
            this.queryParams = {
                pushScope: '',
                status: '',
                pageNum: 1,
                pageSize: 10
            };
            this.getList();
        },

        // 获取列表
        async getList() {
            this.loading = true;
            try {
                let res = await getPolicyList(this.queryParams);
                if (res.code === 200) {
                    this.total = res.total;
                    this.list = res.rows;
                }
            } catch (error) {
                console.error('获取列表失败:', error);
                this.$message({
                    message: '获取列表失败',
                    type: 'error'
                });
            } finally {
                this.loading = false;
            }
        }
    },
}

</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    background: #FFFFFF;
    border-radius: 14px;
    position: relative;
}
</style>
