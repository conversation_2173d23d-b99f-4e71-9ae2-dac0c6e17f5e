import request from '@/utils/request'

// 查询政策发送列表
export function getPolicyList(query) {
  return request({
    url: '/manage/policy/list',
    method: 'get',
    params: query
  })
}

// 查询政策发送详细
export function getPolicy(policyId) {
  return request({
    url: '/manage/policy/' + policyId,
    method: 'get'
  })
}

// 新增政策发送
export function addPolicy(data) {
  return request({
    url: '/manage/policy',
    method: 'post',
    data: data
  })
}

// 修改政策发送
export function updatePolicy(data) {
  return request({
    url: '/manage/policy',
    method: 'put',
    data: data
  })
}

// 删除政策发送
export function delPolicy(policyId) {
  return request({
    url: '/manage/policy/' + policyId,
    method: 'delete'
  })
}

// 审核政策发送
export function approvePolicy(data) {
  return request({
    url: '/manage/policy/approve',
    method: 'post',
    data: data
  })
}
