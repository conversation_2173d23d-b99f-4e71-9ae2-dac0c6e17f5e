# 政策发送页面功能测试

## 页面功能概述

该页面已从联系人管理页面修改为政策发送页面，具备以下功能：

### 1. 列表展示
- 推送范围（属性/单位）
- 发送时间
- 状态（待审核/已审核）
- 操作按钮（查看、编辑、删除、审核）

### 2. 权限控制
- **消息创建人员** (message-created): 可以新建、编辑、删除，但不能审核
- **消息审核人员** (message-checked): 可以审核，但不能新建

### 3. 新建功能
- 推送范围选择（单选）：
  - 属性：需要选择行业、部门、属地
  - 单位：需要选择单位名称
- 内容编辑（使用富文本编辑器）
- 附件上传（可选）
- 发送按钮

### 4. 状态管理
- 待审核状态：可以修改和删除
- 已审核状态：只能查看

### 5. 审核功能
- 审核结果：通过/拒绝
- 审核意见：必填

## 技术实现

### API接口
- `getPolicyList`: 获取政策发送列表
- `addPolicy`: 新增政策发送
- `updatePolicy`: 修改政策发送
- `getPolicy`: 获取政策发送详情
- `delPolicy`: 删除政策发送
- `approvePolicy`: 审核政策发送

### 组件使用
- `Editor`: 富文本编辑器组件
- `FileUpload`: 文件上传组件
- `v-hasPermi`: 权限控制指令

### 表单验证
- 动态验证规则，根据推送范围自动调整必填字段
- 推送范围为"属性"时：行业、部门、属地为必填
- 推送范围为"单位"时：单位名称为必填
- 内容始终为必填

## 测试要点

1. 权限控制是否正确
2. 表单验证是否按预期工作
3. 富文本编辑器是否正常
4. 文件上传功能是否正常
5. 审核流程是否完整
6. 查看模式是否为只读
